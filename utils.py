import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
import os
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

def save_training_results(results, filepath='results/training_results.json'):
    """Save training results to JSON file"""
    
    # Create a serializable version of results
    serializable_results = {}
    
    for model_name, result in results.items():
        if 'error' in result:
            serializable_results[model_name] = {'error': result['error']}
        else:
            # Extract serializable data
            model_result = {
                'test_accuracy': result['test_results']['accuracy'],
                'training_history': result['history'],
                'classification_report': result['test_results']['classification_report']
            }
            
            # Add model-specific info
            if model_name == 'random_forest':
                model_result['n_estimators_final'] = result['model'].model.n_estimators
            elif model_name == 'lstm':
                model_result['vocab_size'] = len(result['word_to_idx'])
                model_result['model_type'] = result['model'].model_type
            elif model_name == 'bert':
                if result['model'] is not None:
                    model_result['model_name'] = result['model'].model_name
                else:
                    model_result['model_name'] = 'bert-base-uncased (simulated)'
            elif model_name == 'logistic_regression':
                model_result['max_features'] = result['model'].max_features
            
            serializable_results[model_name] = model_result
    
    # Save to JSON
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, 'w') as f:
        json.dump(serializable_results, f, indent=2)
    
    print(f"Training results saved to {filepath}")

def plot_training_history(results, save_path='results/training_plots.html'):
    """Create interactive plots of training history"""
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=['Random Forest', 'Logistic Regression', 'LSTM', 'BERT'],
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": True}, {"secondary_y": True}]]
    )
    
    colors = ['blue', 'red', 'green', 'orange']
    model_names = ['random_forest', 'logistic_regression', 'lstm', 'bert']
    
    for i, (model_name, color) in enumerate(zip(model_names, colors)):
        row = (i // 2) + 1
        col = (i % 2) + 1
        
        if model_name in results and 'error' not in results[model_name]:
            history = results[model_name]['history']
            epochs = [h['epoch'] for h in history]
            
            if model_name in ['random_forest', 'logistic_regression']:
                # For traditional ML models, plot accuracy only
                train_acc = [h['train_accuracy'] for h in history]
                val_acc = [h['val_accuracy'] for h in history]
                
                fig.add_trace(
                    go.Scatter(x=epochs, y=train_acc, name=f'{model_name} Train Acc',
                              line=dict(color=color, dash='solid')),
                    row=row, col=col
                )
                fig.add_trace(
                    go.Scatter(x=epochs, y=val_acc, name=f'{model_name} Val Acc',
                              line=dict(color=color, dash='dash')),
                    row=row, col=col
                )
            else:
                # For deep learning models, plot both loss and accuracy
                train_loss = [h['train_loss'] for h in history]
                val_loss = [h['val_loss'] for h in history]
                train_acc = [h['train_accuracy'] for h in history]
                val_acc = [h['val_accuracy'] for h in history]
                
                # Add loss traces
                fig.add_trace(
                    go.Scatter(x=epochs, y=train_loss, name=f'{model_name} Train Loss',
                              line=dict(color=color, dash='solid'), yaxis='y2'),
                    row=row, col=col, secondary_y=True
                )
                fig.add_trace(
                    go.Scatter(x=epochs, y=val_loss, name=f'{model_name} Val Loss',
                              line=dict(color=color, dash='dash'), yaxis='y2'),
                    row=row, col=col, secondary_y=True
                )
                
                # Add accuracy traces
                fig.add_trace(
                    go.Scatter(x=epochs, y=train_acc, name=f'{model_name} Train Acc',
                              line=dict(color='darkgreen', dash='solid')),
                    row=row, col=col
                )
                fig.add_trace(
                    go.Scatter(x=epochs, y=val_acc, name=f'{model_name} Val Acc',
                              line=dict(color='darkred', dash='dash')),
                    row=row, col=col
                )
    
    # Update layout
    fig.update_layout(
        title="Training History for All Models",
        height=800,
        showlegend=True
    )
    
    # Save plot
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    fig.write_html(save_path)
    print(f"Training plots saved to {save_path}")
    
    return fig

def create_confusion_matrices(results, label_encoder, save_path='results/confusion_matrices.html'):
    """Create confusion matrices for all models"""
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=['Random Forest', 'Logistic Regression', 'LSTM', 'BERT'],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "heatmap"}, {"type": "heatmap"}]]
    )
    
    model_names = ['random_forest', 'logistic_regression', 'lstm', 'bert']
    
    for i, model_name in enumerate(model_names):
        row = (i // 2) + 1
        col = (i % 2) + 1
        
        if model_name in results and 'error' not in results[model_name]:
            test_results = results[model_name]['test_results']
            y_true = test_results.get('labels', [])
            y_pred = test_results['predictions']
            
            if len(y_true) > 0:
                cm = confusion_matrix(y_true, y_pred)
                
                # Create heatmap
                fig.add_trace(
                    go.Heatmap(
                        z=cm,
                        x=label_encoder.classes_,
                        y=label_encoder.classes_,
                        colorscale='Blues',
                        showscale=True if i == 0 else False
                    ),
                    row=row, col=col
                )
    
    fig.update_layout(
        title="Confusion Matrices for All Models",
        height=800
    )
    
    # Save plot
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    fig.write_html(save_path)
    print(f"Confusion matrices saved to {save_path}")
    
    return fig

def create_model_comparison(results, save_path='results/model_comparison.html'):
    """Create a comparison chart of all models"""
    
    model_names = []
    accuracies = []
    
    for model_name, result in results.items():
        if 'error' not in result:
            model_names.append(model_name.replace('_', ' ').title())
            accuracies.append(result['test_results']['accuracy'])
    
    # Create bar chart
    fig = go.Figure(data=[
        go.Bar(
            x=model_names,
            y=accuracies,
            text=[f'{acc:.4f}' for acc in accuracies],
            textposition='auto',
            marker_color=['blue', 'red', 'green', 'orange'][:len(model_names)]
        )
    ])
    
    fig.update_layout(
        title="Model Performance Comparison",
        xaxis_title="Models",
        yaxis_title="Test Accuracy",
        yaxis=dict(range=[0, 1]),
        height=500
    )
    
    # Save plot
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    fig.write_html(save_path)
    print(f"Model comparison saved to {save_path}")
    
    return fig

def generate_classification_report_table(results, label_encoder):
    """Generate a comprehensive classification report table"""
    
    report_data = []
    
    for model_name, result in results.items():
        if 'error' not in result:
            report = result['test_results']['classification_report']
            
            for class_name, metrics in report.items():
                if isinstance(metrics, dict) and class_name not in ['accuracy', 'macro avg', 'weighted avg']:
                    # Convert class index back to original label
                    try:
                        original_label = label_encoder.inverse_transform([int(class_name)])[0]
                    except:
                        original_label = class_name
                    
                    report_data.append({
                        'Model': model_name.replace('_', ' ').title(),
                        'Class': original_label,
                        'Precision': metrics['precision'],
                        'Recall': metrics['recall'],
                        'F1-Score': metrics['f1-score'],
                        'Support': metrics['support']
                    })
    
    return pd.DataFrame(report_data)

def print_model_summary(results):
    """Print a summary of all model results"""
    
    print("\n" + "="*80)
    print("MODEL PERFORMANCE SUMMARY")
    print("="*80)
    
    for model_name, result in results.items():
        print(f"\n{model_name.upper().replace('_', ' ')}:")
        print("-" * 40)
        
        if 'error' in result:
            print(f"❌ Training failed: {result['error']}")
        else:
            accuracy = result['test_results']['accuracy']
            print(f"✅ Test Accuracy: {accuracy:.4f}")
            
            # Print additional model-specific info
            if model_name == 'random_forest':
                n_trees = result['model'].model.n_estimators
                print(f"   Final number of trees: {n_trees}")
            elif model_name == 'lstm':
                vocab_size = len(result['word_to_idx'])
                print(f"   Vocabulary size: {vocab_size}")
            elif model_name == 'bert':
                model_type = result['model'].model_name
                print(f"   Model type: {model_type}")
            elif model_name == 'logistic_regression':
                max_features = result['model'].max_features
                print(f"   Max features: {max_features}")
    
    print("\n" + "="*80)

def load_training_results(filepath='results/training_results.json'):
    """Load training results from JSON file"""
    
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Results file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        results = json.load(f)
    
    return results
