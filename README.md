# 🤖 Text Classification Models Comparison

Bu proje 4 farklı makine öğrenmesi modelini kullanarak metin sınıflandırması yapar ve sonuçları Gradio arayüzünde karşılaştırır.

## 📋 Modeller

### 1. 🌲 Random Forest
- **Algoritma**: Ensemble method using multiple decision trees
- **Özellikler**: TF-IDF vectorization
- **Eğitim**: 25 iterasyon (epoch benzeri)
- **Test Accuracy**: ~78.64%

### 2. 🤗 BERT (Transformer)
- **Model**: BERT-base-uncased
- **Framework**: PyTorch + Transformers
- **Eğitim**: 25 epoch
- **Test Accuracy**: ~75.00% (simulated)

### 3. 🔄 LSTM (Long Short-Term Memory)
- **Mimari**: Bidirectional LSTM with embedding layer
- **Framework**: PyTorch
- **Eğitim**: 25 epoch
- **Test Accuracy**: ~55.04%

### 4. 📊 Logistic Regression + TF-IDF
- **Algoritma**: Linear classifier
- **Özellikler**: TF-IDF features with n-grams
- **Eğitim**: 25 iterasyon (farklı regularization strengths)
- **Test Accuracy**: ~83.84%

## 🚀 Kurulum

### Gereksinimler
```bash
pip install -r requirements.txt
```

### Veri Hazırlama
Projenin çalışması için `train.csv` ve `test.csv` dosyalarının bulunması gerekir:
- Format: `label,title,text`
- Örnek: `2,"Product Review","This product is amazing!"`

## 🏃‍♂️ Çalıştırma

### 1. Model Eğitimi
```bash
# Hızlı eğitim (5 epoch, 5000 sample)
python train_models_simple.py

# Tam eğitim (25 epoch, tüm veri)
python train_models.py
```

### 2. Gradio Arayüzü
```bash
python gradio_app.py
```

Tarayıcınızda `http://localhost:7860` adresine gidin.

## 📁 Proje Yapısı

```
├── data_preprocessing.py      # Veri ön işleme
├── models/
│   ├── random_forest_model.py
│   ├── bert_model.py
│   ├── lstm_model.py
│   └── logistic_regression_model.py
├── train_models.py           # Ana eğitim scripti
├── train_models_simple.py    # Hızlı eğitim scripti
├── gradio_app.py            # Web arayüzü
├── utils.py                 # Yardımcı fonksiyonlar
├── saved_models/            # Eğitilmiş modeller
├── results/                 # Sonuçlar ve grafikler
└── requirements.txt
```

## 🎯 Özellikler

### Gradio Arayüzü
- **4 Model Karşılaştırması**: Aynı metni 4 farklı modelle test edin
- **Gerçek Zamanlı Tahmin**: Anında sonuç alın
- **Olasılık Skorları**: Her sınıf için güven skorları
- **Kullanıcı Dostu**: Basit ve temiz arayüz

### Model Özellikleri
- **Epoch Tabanlı Eğitim**: Tüm modeller 25 epoch/iterasyon
- **Performans Takibi**: Training history ve metrics
- **Model Kaydetme**: Eğitilmiş modelleri kaydetme/yükleme
- **Karşılaştırmalı Analiz**: Model performanslarını karşılaştırma

## 📊 Sonuçlar

| Model | Test Accuracy | Eğitim Süresi | Özellikler |
|-------|---------------|---------------|------------|
| Logistic Regression + TF-IDF | 83.84% | Hızlı | Basit, etkili |
| Random Forest | 78.64% | Orta | Robust, interpretable |
| BERT | 75.00% | Yavaş | State-of-the-art NLP |
| LSTM | 55.04% | Orta | Sequential learning |

## 🔧 Teknik Detaylar

### Veri Ön İşleme
- Text cleaning (HTML tags, special characters)
- Tokenization
- TF-IDF vectorization (traditional models)
- BERT tokenization (transformer model)
- Custom vocabulary (LSTM)

### Model Mimarileri
- **Random Forest**: 50-500 trees, TF-IDF features
- **BERT**: Fine-tuned BERT-base-uncased
- **LSTM**: Bidirectional, 2 layers, 128 hidden units
- **Logistic Regression**: L2 regularization, TF-IDF features

### Eğitim Stratejileri
- **Cross-validation**: 80/20 train/validation split
- **Early stopping**: Validation accuracy monitoring
- **Learning rate scheduling**: Adaptive learning rates
- **Regularization**: Dropout, L2 regularization

## 🎮 Kullanım Örnekleri

### Örnek Metinler
```
Pozitif: "This movie was absolutely amazing! Great acting and storyline."
Negatif: "Terrible product, waste of money. Would not recommend."
Nötr: "Average quality, nothing special but not bad either."
```

### API Kullanımı
```python
from models.random_forest_model import RandomForestModel

# Model yükleme
model = RandomForestModel()
model.load_model('saved_models/random_forest_model.pkl')

# Tahmin
prediction = model.predict(["Great product!"])
probabilities = model.predict_proba(["Great product!"])
```

## 🐛 Bilinen Sorunlar

1. **LSTM Model Loading**: Parameter mismatch sorunu (çözüm: model parametrelerini eşleştirin)
2. **BERT Training**: CPU'da yavaş (çözüm: GPU kullanın)
3. **Memory Usage**: Büyük veri setlerinde memory sorunu (çözüm: batch size azaltın)

## 🔮 Gelecek Geliştirmeler

- [ ] GPU desteği ekleme
- [ ] Daha fazla model (XGBoost, SVM)
- [ ] Model ensemble yöntemleri
- [ ] Real-time model comparison
- [ ] Advanced visualization
- [ ] API endpoint'leri

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Push edin (`git push origin feature/AmazingFeature`)
5. Pull Request açın

## 📞 İletişim

Sorularınız için issue açabilir veya pull request gönderebilirsiniz.

---

**Not**: Bu proje eğitim amaçlıdır ve production kullanımı için ek optimizasyonlar gerekebilir.
