import gradio as gr
import pandas as pd
import numpy as np
import torch
import joblib
import os
import json
from transformers import AutoTokenizer
import warnings
warnings.filterwarnings('ignore')

# Import our models
from models.random_forest_model import RandomForestModel
from models.bert_model import BERTModel
from models.lstm_model import LSTMGRUModel
from models.logistic_regression_model import LogisticRegressionTFIDFModel
from data_preprocessing import clean_text, text_to_sequence

class ModelPredictor:
    def __init__(self):
        self.models = {}
        self.label_encoders = {}
        self.load_all_models()
    
    def load_all_models(self):
        """Load all trained models"""
        print("Loading trained models...")
        
        # Load Random Forest
        try:
            rf_model = RandomForestModel()
            rf_model.load_model('saved_models/random_forest_model.pkl')
            self.models['random_forest'] = rf_model
            print("✅ Random Forest model loaded")
        except Exception as e:
            print(f"❌ Failed to load Random Forest: {e}")
        
        # Load Logistic Regression
        try:
            lr_model = LogisticRegressionTFIDFModel()
            lr_model.load_model('saved_models/logistic_regression_model.pkl')
            self.models['logistic_regression'] = lr_model
            print("✅ Logistic Regression model loaded")
        except Exception as e:
            print(f"❌ Failed to load Logistic Regression: {e}")
        
        # Load LSTM
        try:
            # We need to load vocab info for LSTM
            if os.path.exists('saved_models/lstm_vocab.json'):
                with open('saved_models/lstm_vocab.json', 'r') as f:
                    vocab_data = json.load(f)
                    word_to_idx = vocab_data['word_to_idx']
                    vocab_size = len(word_to_idx)
            else:
                # Create dummy vocab if not available
                vocab_size = 10000
                word_to_idx = {'<PAD>': 0, '<UNK>': 1}

            # Use the same parameters as in training
            lstm_model = LSTMGRUModel(
                vocab_size=vocab_size,
                model_type='lstm',
                embedding_dim=64,  # Match training parameters
                hidden_dim=128,    # Match training parameters
                num_classes=2
            )
            lstm_model.load_model('saved_models/lstm_model.pth')
            self.models['lstm'] = lstm_model
            self.models['lstm_vocab'] = word_to_idx
            print("✅ LSTM model loaded")
        except Exception as e:
            print(f"❌ Failed to load LSTM: {e}")
            # Create a dummy LSTM predictor
            self.models['lstm'] = None
        
        # Load BERT
        try:
            bert_model = BERTModel()
            bert_model.load_model('saved_models/bert_model.pth')
            self.models['bert'] = bert_model
            print("✅ BERT model loaded")
        except Exception as e:
            print(f"❌ Failed to load BERT: {e}")
        
        # Load label encoder (assuming it's the same for all models)
        try:
            if os.path.exists('saved_models/label_encoder.pkl'):
                self.label_encoder = joblib.load('saved_models/label_encoder.pkl')
            else:
                # Create dummy label encoder
                from sklearn.preprocessing import LabelEncoder
                self.label_encoder = LabelEncoder()
                self.label_encoder.classes_ = np.array(['1', '2', '3', '4', '5'])  # Common rating classes
            print("✅ Label encoder loaded")
        except Exception as e:
            print(f"❌ Failed to load label encoder: {e}")
            # Create dummy label encoder
            from sklearn.preprocessing import LabelEncoder
            self.label_encoder = LabelEncoder()
            self.label_encoder.classes_ = np.array(['1', '2', '3', '4', '5'])
    
    def predict_random_forest(self, text):
        """Predict using Random Forest model"""
        if 'random_forest' not in self.models:
            return "Model not available", {}
        
        try:
            cleaned_text = clean_text(text)
            if not cleaned_text:
                return "Please enter some text", {}
            
            model = self.models['random_forest']
            
            # Transform text using the model's vectorizer
            # We need to access the vectorizer from training results
            if hasattr(model, 'vectorizer'):
                text_tfidf = model.vectorizer.transform([cleaned_text])
            else:
                return "Vectorizer not available", {}
            
            prediction = model.predict(text_tfidf.toarray())[0]
            probabilities = model.predict_proba(text_tfidf.toarray())[0]
            
            # Convert prediction back to original label
            predicted_label = self.label_encoder.inverse_transform([prediction])[0]
            
            # Create probability dictionary
            prob_dict = {}
            for i, prob in enumerate(probabilities):
                label = self.label_encoder.inverse_transform([i])[0]
                prob_dict[f"Class {label}"] = float(prob)
            
            return f"Predicted Class: {predicted_label}", prob_dict
            
        except Exception as e:
            return f"Error: {str(e)}", {}
    
    def predict_logistic_regression(self, text):
        """Predict using Logistic Regression model"""
        if 'logistic_regression' not in self.models:
            return "Model not available", {}
        
        try:
            cleaned_text = clean_text(text)
            if not cleaned_text:
                return "Please enter some text", {}
            
            model = self.models['logistic_regression']
            
            prediction = model.predict([cleaned_text])[0]
            probabilities = model.predict_proba([cleaned_text])[0]
            
            # Convert prediction back to original label
            predicted_label = self.label_encoder.inverse_transform([prediction])[0]
            
            # Create probability dictionary
            prob_dict = {}
            for i, prob in enumerate(probabilities):
                label = self.label_encoder.inverse_transform([i])[0]
                prob_dict[f"Class {label}"] = float(prob)
            
            return f"Predicted Class: {predicted_label}", prob_dict
            
        except Exception as e:
            return f"Error: {str(e)}", {}
    
    def predict_lstm(self, text):
        """Predict using LSTM model"""
        if 'lstm' not in self.models or self.models['lstm'] is None:
            return "LSTM model not available", {"Note": "Model failed to load"}

        try:
            cleaned_text = clean_text(text)
            if not cleaned_text:
                return "Please enter some text", {}

            model = self.models['lstm']
            word_to_idx = self.models.get('lstm_vocab', {'<PAD>': 0, '<UNK>': 1})

            # Convert text to sequence
            sequence = text_to_sequence(cleaned_text, word_to_idx, max_length=128)  # Match training
            sequence_tensor = torch.tensor([sequence])

            predictions, probabilities = model.predict(sequence_tensor)
            prediction = predictions[0]
            probs = probabilities[0]

            # Convert prediction back to original label
            predicted_label = self.label_encoder.inverse_transform([prediction])[0]

            # Create probability dictionary
            prob_dict = {}
            for i, prob in enumerate(probs):
                label = self.label_encoder.inverse_transform([i])[0]
                prob_dict[f"Class {label}"] = float(prob)

            return f"Predicted Class: {predicted_label}", prob_dict

        except Exception as e:
            return f"Error: {str(e)}", {}
    
    def predict_bert(self, text):
        """Predict using BERT model"""
        if 'bert' not in self.models:
            return "Model not available", {}
        
        try:
            cleaned_text = clean_text(text)
            if not cleaned_text:
                return "Please enter some text", {}
            
            model = self.models['bert']
            
            predictions, probabilities = model.predict([cleaned_text])
            prediction = predictions[0]
            probs = probabilities[0]
            
            # Convert prediction back to original label
            predicted_label = self.label_encoder.inverse_transform([prediction])[0]
            
            # Create probability dictionary
            prob_dict = {}
            for i, prob in enumerate(probs):
                label = self.label_encoder.inverse_transform([i])[0]
                prob_dict[f"Class {label}"] = float(prob)
            
            return f"Predicted Class: {predicted_label}", prob_dict
            
        except Exception as e:
            return f"Error: {str(e)}", {}

# Initialize predictor
predictor = ModelPredictor()

# Create Gradio interface
def create_gradio_app():
    """Create the Gradio application"""
    
    with gr.Blocks(title="Text Classification Models Comparison", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🤖 Text Classification Models Comparison")
        gr.Markdown("Compare predictions from 4 different models: Random Forest, BERT, LSTM, and Logistic Regression + TF-IDF")
        
        with gr.Row():
            with gr.Column(scale=2):
                text_input = gr.Textbox(
                    label="Enter your text here:",
                    placeholder="Type your review or text to classify...",
                    lines=5
                )
                
                predict_btn = gr.Button("🔮 Predict with All Models", variant="primary", size="lg")
                
                gr.Markdown("### 📝 Example texts to try:")
                gr.Markdown("""
                - "This movie was absolutely amazing! Great acting and storyline."
                - "Terrible product, waste of money. Would not recommend."
                - "Average quality, nothing special but not bad either."
                """)
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 🌲 Random Forest Results")
                rf_output = gr.Textbox(label="Prediction", interactive=False)
                rf_probs = gr.Label(label="Class Probabilities")
            
            with gr.Column():
                gr.Markdown("## 🤗 BERT Results")
                bert_output = gr.Textbox(label="Prediction", interactive=False)
                bert_probs = gr.Label(label="Class Probabilities")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 🔄 LSTM Results")
                lstm_output = gr.Textbox(label="Prediction", interactive=False)
                lstm_probs = gr.Label(label="Class Probabilities")
            
            with gr.Column():
                gr.Markdown("## 📊 Logistic Regression + TF-IDF Results")
                lr_output = gr.Textbox(label="Prediction", interactive=False)
                lr_probs = gr.Label(label="Class Probabilities")
        
        # Model information
        with gr.Accordion("ℹ️ Model Information", open=False):
            gr.Markdown("""
            ### Model Details:
            
            **🌲 Random Forest**: Ensemble method using multiple decision trees with TF-IDF features
            - Trained for 25 iterations with increasing complexity
            - Uses TF-IDF vectorization for text features
            
            **🤗 BERT**: Transformer-based pre-trained language model
            - Fine-tuned BERT-base-uncased for 25 epochs
            - Uses attention mechanisms for context understanding
            
            **🔄 LSTM**: Long Short-Term Memory neural network
            - Bidirectional LSTM with embedding layer
            - Trained for 25 epochs with custom vocabulary
            
            **📊 Logistic Regression + TF-IDF**: Traditional machine learning approach
            - Linear classifier with TF-IDF features
            - Trained for 25 iterations with different regularization strengths
            """)
        
        def predict_all_models(text):
            """Predict using all models"""
            if not text.strip():
                empty_result = ("Please enter some text", {})
                return empty_result * 8  # 4 models × 2 outputs each
            
            # Get predictions from all models
            rf_pred, rf_prob = predictor.predict_random_forest(text)
            bert_pred, bert_prob = predictor.predict_bert(text)
            lstm_pred, lstm_prob = predictor.predict_lstm(text)
            lr_pred, lr_prob = predictor.predict_logistic_regression(text)
            
            return (rf_pred, rf_prob, bert_pred, bert_prob, 
                   lstm_pred, lstm_prob, lr_pred, lr_prob)
        
        # Connect the predict button to all outputs
        predict_btn.click(
            fn=predict_all_models,
            inputs=[text_input],
            outputs=[rf_output, rf_probs, bert_output, bert_probs,
                    lstm_output, lstm_probs, lr_output, lr_probs]
        )
        
        # Also trigger on Enter key
        text_input.submit(
            fn=predict_all_models,
            inputs=[text_input],
            outputs=[rf_output, rf_probs, bert_output, bert_probs,
                    lstm_output, lstm_probs, lr_output, lr_probs]
        )
    
    return app

if __name__ == "__main__":
    # Create and launch the app
    app = create_gradio_app()
    
    print("🚀 Starting Gradio app...")
    print("📊 All models loaded and ready!")
    
    # Launch the app
    app.launch(
        server_name="0.0.0.0",  # Allow external access
        server_port=7860,       # Default Gradio port
        share=False,            # Set to True to create a public link
        debug=True
    )
