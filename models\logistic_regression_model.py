import numpy as np
import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.feature_extraction.text import TfidfVectorizer
import joblib
import os
from tqdm import tqdm
import time

class LogisticRegressionTFIDFModel:
    def __init__(self, max_features=10000, random_state=42, max_iter=1000):
        self.vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        
        self.model = LogisticRegression(
            random_state=random_state,
            max_iter=max_iter,
            solver='liblinear'  # Good for small datasets
        )
        
        self.is_trained = False
        self.training_history = []
        self.max_features = max_features
        
    def prepare_features(self, train_texts, test_texts=None):
        """Prepare TF-IDF features"""
        print("Creating TF-IDF features...")
        
        # Fit vectorizer on training data
        X_train_tfidf = self.vectorizer.fit_transform(train_texts)
        
        if test_texts is not None:
            X_test_tfidf = self.vectorizer.transform(test_texts)
            return X_train_tfidf, X_test_tfidf
        
        return X_train_tfidf
    
    def train_with_epochs(self, X_train, y_train, X_val, y_val, epochs=25):
        """
        Train Logistic Regression with epoch-like iterations
        Since LR doesn't have epochs, we'll use different regularization strengths
        and partial fitting approaches
        """
        print(f"Training Logistic Regression + TF-IDF for {epochs} iterations...")
        
        # Different C values (regularization strengths) for each "epoch"
        C_values = np.logspace(-3, 2, epochs)  # From 0.001 to 100
        
        best_val_acc = 0
        best_model = None
        
        for epoch in tqdm(range(epochs), desc="Training LR"):
            start_time = time.time()
            
            # Use different regularization strength for each epoch
            current_C = C_values[epoch]
            
            # Create model with current C value
            current_model = LogisticRegression(
                C=current_C,
                random_state=42,
                max_iter=1000,
                solver='liblinear'
            )
            
            # For variety, use different subsets of training data
            n_samples = X_train.shape[0]
            sample_size = max(1000, int(n_samples * (0.5 + 0.5 * epoch / epochs)))

            # Random sampling for this epoch
            np.random.seed(42 + epoch)
            sample_indices = np.random.choice(n_samples, size=min(sample_size, n_samples), replace=False)

            X_epoch = X_train[sample_indices]
            y_epoch = y_train[sample_indices]
            
            # Train model
            current_model.fit(X_epoch, y_epoch)
            
            # Evaluate
            train_pred = current_model.predict(X_train)
            val_pred = current_model.predict(X_val)
            
            train_acc = accuracy_score(y_train, train_pred)
            val_acc = accuracy_score(y_val, val_pred)
            
            # Keep the best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_model = current_model
                self.model = current_model
            
            epoch_time = time.time() - start_time
            
            # Store training history
            self.training_history.append({
                'epoch': epoch + 1,
                'train_accuracy': train_acc,
                'val_accuracy': val_acc,
                'C_value': current_C,
                'sample_size': len(X_epoch),
                'time': epoch_time
            })
            
            if (epoch + 1) % 5 == 0:
                print(f"Epoch {epoch+1}/{epochs} - Train Acc: {train_acc:.4f} - Val Acc: {val_acc:.4f} - C: {current_C:.4f}")
        
        self.is_trained = True
        print("Logistic Regression + TF-IDF training completed!")
        print(f"Best validation accuracy: {best_val_acc:.4f}")
        
        return self.training_history
    
    def predict(self, texts):
        """Make predictions on text data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Transform texts to TF-IDF features
        X_tfidf = self.vectorizer.transform(texts)
        return self.model.predict(X_tfidf)
    
    def predict_proba(self, texts):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Transform texts to TF-IDF features
        X_tfidf = self.vectorizer.transform(texts)
        return self.model.predict_proba(X_tfidf)
    
    def evaluate(self, test_texts, y_test):
        """Evaluate model performance"""
        predictions = self.predict(test_texts)
        probabilities = self.predict_proba(test_texts)
        
        accuracy = accuracy_score(y_test, predictions)
        report = classification_report(y_test, predictions, output_dict=True)
        
        return {
            'accuracy': accuracy,
            'predictions': predictions,
            'probabilities': probabilities,
            'classification_report': report
        }
    
    def save_model(self, filepath):
        """Save the trained model"""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_data = {
            'model': self.model,
            'vectorizer': self.vectorizer,
            'training_history': self.training_history,
            'is_trained': self.is_trained,
            'max_features': self.max_features
        }
        
        joblib.dump(model_data, filepath)
        print(f"Logistic Regression + TF-IDF model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load a trained model"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Model file not found: {filepath}")
        
        model_data = joblib.load(filepath)
        self.model = model_data['model']
        self.vectorizer = model_data['vectorizer']
        self.training_history = model_data['training_history']
        self.is_trained = model_data['is_trained']
        self.max_features = model_data['max_features']
        
        print(f"Logistic Regression + TF-IDF model loaded from {filepath}")
    
    def get_feature_importance(self, top_n=20):
        """Get most important features (words) for each class"""
        if not self.is_trained:
            raise ValueError("Model must be trained before getting feature importance")
        
        feature_names = self.vectorizer.get_feature_names_out()
        
        # Get coefficients for each class
        if len(self.model.classes_) == 2:
            # Binary classification
            coefficients = self.model.coef_[0]
            
            # Get top positive and negative features
            top_positive_idx = np.argsort(coefficients)[-top_n:][::-1]
            top_negative_idx = np.argsort(coefficients)[:top_n]
            
            positive_features = [(feature_names[i], coefficients[i]) for i in top_positive_idx]
            negative_features = [(feature_names[i], coefficients[i]) for i in top_negative_idx]
            
            return {
                'positive_features': positive_features,
                'negative_features': negative_features
            }
        else:
            # Multi-class classification
            feature_importance = {}
            for i, class_name in enumerate(self.model.classes_):
                coefficients = self.model.coef_[i]
                top_idx = np.argsort(np.abs(coefficients))[-top_n:][::-1]
                top_features = [(feature_names[j], coefficients[j]) for j in top_idx]
                feature_importance[f'class_{class_name}'] = top_features
            
            return feature_importance
    
    def get_vocabulary_stats(self):
        """Get statistics about the vocabulary"""
        if not hasattr(self.vectorizer, 'vocabulary_'):
            raise ValueError("Vectorizer must be fitted before getting vocabulary stats")
        
        vocab_size = len(self.vectorizer.vocabulary_)
        feature_names = self.vectorizer.get_feature_names_out()
        
        return {
            'vocabulary_size': vocab_size,
            'max_features': self.max_features,
            'sample_features': feature_names[:20].tolist() if len(feature_names) > 20 else feature_names.tolist()
        }
