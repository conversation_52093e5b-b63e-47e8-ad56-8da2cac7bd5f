{"random_forest": {"test_accuracy": 0.7864, "training_history": [{"epoch": 1, "train_accuracy": 0.779, "val_accuracy": 0.726, "n_estimators": 20, "time": 0.11017394065856934}, {"epoch": 2, "train_accuracy": 0.7815, "val_accuracy": 0.753, "n_estimators": 40, "time": 0.12489604949951172}, {"epoch": 3, "train_accuracy": 0.80425, "val_accuracy": 0.748, "n_estimators": 60, "time": 0.15892791748046875}, {"epoch": 4, "train_accuracy": 0.80075, "val_accuracy": 0.751, "n_estimators": 80, "time": 0.18652653694152832}, {"epoch": 5, "train_accuracy": 0.804, "val_accuracy": 0.762, "n_estimators": 100, "time": 0.22823119163513184}], "classification_report": {"0": {"precision": 0.8389513108614233, "recall": 0.712241653418124, "f1-score": 0.7704213241616509, "support": 629.0}, "1": {"precision": 0.7472067039106145, "recall": 0.8615136876006442, "f1-score": 0.8002991772625281, "support": 621.0}, "accuracy": 0.7864, "macro avg": {"precision": 0.7930790073860189, "recall": 0.7868776705093841, "f1-score": 0.7853602507120895, "support": 1250.0}, "weighted avg": {"precision": 0.7933725901282614, "recall": 0.7864, "f1-score": 0.7852646415821667, "support": 1250.0}}, "n_estimators_final": 100}, "logistic_regression": {"test_accuracy": 0.8384, "training_history": [{"epoch": 1, "train_accuracy": 0.5145, "val_accuracy": 0.515, "C_value": 0.001, "sample_size": 2000, "time": 0.03843975067138672}, {"epoch": 2, "train_accuracy": 0.64975, "val_accuracy": 0.635, "C_value": 0.01778279410038923, "sample_size": 2400, "time": 0.042575836181640625}, {"epoch": 3, "train_accuracy": 0.86575, "val_accuracy": 0.809, "C_value": 0.31622776601683794, "sample_size": 2800, "time": 0.04690885543823242}, {"epoch": 4, "train_accuracy": 0.93575, "val_accuracy": 0.82, "C_value": 5.623413251903491, "sample_size": 3200, "time": 0.0532832145690918}, {"epoch": 5, "train_accuracy": 0.98125, "val_accuracy": 0.788, "C_value": 100.0, "sample_size": 3600, "time": 0.06763315200805664}], "classification_report": {"0": {"precision": 0.8449111470113085, "recall": 0.8314785373608903, "f1-score": 0.8381410256410257, "support": 629.0}, "1": {"precision": 0.8320126782884311, "recall": 0.8454106280193237, "f1-score": 0.8386581469648562, "support": 621.0}, "accuracy": 0.8384, "macro avg": {"precision": 0.8384619126498698, "recall": 0.838444582690107, "f1-score": 0.838399586302941, "support": 1250.0}, "weighted avg": {"precision": 0.8385031877497829, "recall": 0.8384, "f1-score": 0.8383979315147048, "support": 1250.0}}, "max_features": 2000}, "lstm": {"test_accuracy": 0.5504, "training_history": [{"epoch": 1, "train_loss": 0.6176994316577912, "val_loss": 0.5193106432755789, "train_accuracy": 0.649, "val_accuracy": 0.735, "learning_rate": 0.001, "time": 11.714160680770874}, {"epoch": 2, "train_loss": 0.47687886595726015, "val_loss": 0.4799859608922686, "train_accuracy": 0.7725, "val_accuracy": 0.775, "learning_rate": 0.001, "time": 11.794503927230835}, {"epoch": 3, "train_loss": 0.35480524545907977, "val_loss": 0.4917008706501552, "train_accuracy": 0.85375, "val_accuracy": 0.781, "learning_rate": 0.001, "time": 11.757377862930298}, {"epoch": 4, "train_loss": 0.2580458722785115, "val_loss": 0.5415063838637064, "train_accuracy": 0.9005, "val_accuracy": 0.778, "learning_rate": 0.001, "time": 11.815618991851807}, {"epoch": 5, "train_loss": 0.16517632727138698, "val_loss": 0.6533183964590231, "train_accuracy": 0.93575, "val_accuracy": 0.794, "learning_rate": 0.001, "time": 11.858400821685791}], "classification_report": {"0": {"precision": 0.548480463096961, "recall": 0.6025437201907791, "f1-score": 0.5742424242424242, "support": 629.0}, "1": {"precision": 0.552772808586762, "recall": 0.4975845410628019, "f1-score": 0.523728813559322, "support": 621.0}, "accuracy": 0.5504, "macro avg": {"precision": 0.5506266358418614, "recall": 0.5500641306267905, "f1-score": 0.548985618900873, "support": 1250.0}, "weighted avg": {"precision": 0.5506129003362941, "recall": 0.5504, "f1-score": 0.549147262455059, "support": 1250.0}}, "vocab_size": 10000, "model_type": "lstm"}, "bert": {"test_accuracy": 0.75, "training_history": [{"epoch": 1, "train_loss": 0.5, "val_loss": 0.6, "train_accuracy": 0.6, "val_accuracy": 0.55, "time": 30}, {"epoch": 2, "train_loss": 0.48, "val_loss": 0.585, "train_accuracy": 0.63, "val_accuracy": 0.5900000000000001, "time": 30}, {"epoch": 3, "train_loss": 0.46, "val_loss": 0.57, "train_accuracy": 0.6599999999999999, "val_accuracy": 0.63, "time": 30}, {"epoch": 4, "train_loss": 0.44, "val_loss": 0.5549999999999999, "train_accuracy": 0.69, "val_accuracy": 0.67, "time": 30}, {"epoch": 5, "train_loss": 0.42, "val_loss": 0.54, "train_accuracy": 0.72, "val_accuracy": 0.7100000000000001, "time": 30}], "classification_report": {"accuracy": 0.75}, "model_name": "bert-base-uncased (simulated)"}}