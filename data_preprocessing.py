import pandas as pd
import numpy as np
import re
import nltk
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize

class TextDataset(Dataset):
    def __init__(self, texts, labels, tokenizer=None, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        if self.tokenizer:
            # For BERT
            text = str(self.texts[idx])
            label = self.labels[idx]

            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            return {
                'input_ids': encoding['input_ids'].flatten(),
                'attention_mask': encoding['attention_mask'].flatten(),
                'label': torch.tensor(label, dtype=torch.long)
            }
        else:
            # For LSTM/GRU - texts should already be sequences
            text_sequence = self.texts[idx]
            label = self.labels[idx]

            return {
                'text': torch.tensor(text_sequence, dtype=torch.long),
                'label': torch.tensor(label, dtype=torch.long)
            }

def clean_text(text):
    """Clean and preprocess text data"""
    if pd.isna(text):
        return ""
    
    # Convert to string and lowercase
    text = str(text).lower()
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove special characters and digits
    text = re.sub(r'[^a-zA-Z\s]', '', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def preprocess_data(train_path, test_path, sample_size=None):
    """Load and preprocess the dataset"""
    print("Loading data...")
    
    # Load data
    train_df = pd.read_csv(train_path, header=None, names=['label', 'title', 'text'])
    test_df = pd.read_csv(test_path, header=None, names=['label', 'title', 'text'])
    
    # Combine title and text
    train_df['combined_text'] = train_df['title'].astype(str) + ' ' + train_df['text'].astype(str)
    test_df['combined_text'] = test_df['title'].astype(str) + ' ' + test_df['text'].astype(str)
    
    # Clean text
    print("Cleaning text...")
    train_df['cleaned_text'] = train_df['combined_text'].apply(clean_text)
    test_df['cleaned_text'] = test_df['combined_text'].apply(clean_text)
    
    # Remove empty texts
    train_df = train_df[train_df['cleaned_text'].str.len() > 0]
    test_df = test_df[test_df['cleaned_text'].str.len() > 0]
    
    # Sample data if specified (for faster training during development)
    if sample_size:
        train_df = train_df.sample(n=min(sample_size, len(train_df)), random_state=42)
        test_df = test_df.sample(n=min(sample_size//4, len(test_df)), random_state=42)
    
    # Encode labels
    label_encoder = LabelEncoder()
    all_labels = pd.concat([train_df['label'], test_df['label']])
    label_encoder.fit(all_labels)
    
    train_df['encoded_label'] = label_encoder.transform(train_df['label'])
    test_df['encoded_label'] = label_encoder.transform(test_df['label'])
    
    print(f"Train samples: {len(train_df)}")
    print(f"Test samples: {len(test_df)}")
    print(f"Number of classes: {len(label_encoder.classes_)}")
    print(f"Classes: {label_encoder.classes_}")
    
    return train_df, test_df, label_encoder

def create_tfidf_features(train_texts, test_texts, max_features=10000):
    """Create TF-IDF features for traditional ML models"""
    print("Creating TF-IDF features...")
    
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        stop_words='english',
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.95
    )
    
    X_train_tfidf = vectorizer.fit_transform(train_texts)
    X_test_tfidf = vectorizer.transform(test_texts)
    
    return X_train_tfidf, X_test_tfidf, vectorizer

def create_vocab_for_lstm(texts, vocab_size=10000):
    """Create vocabulary for LSTM model"""
    from collections import Counter
    
    # Tokenize all texts
    all_words = []
    for text in texts:
        words = word_tokenize(text.lower())
        all_words.extend(words)
    
    # Create vocabulary
    word_counts = Counter(all_words)
    vocab = ['<PAD>', '<UNK>'] + [word for word, _ in word_counts.most_common(vocab_size-2)]
    
    word_to_idx = {word: idx for idx, word in enumerate(vocab)}
    idx_to_word = {idx: word for word, idx in word_to_idx.items()}
    
    return word_to_idx, idx_to_word

def text_to_sequence(text, word_to_idx, max_length=512):
    """Convert text to sequence of indices"""
    words = word_tokenize(text.lower())
    sequence = [word_to_idx.get(word, word_to_idx['<UNK>']) for word in words]
    
    # Pad or truncate
    if len(sequence) < max_length:
        sequence.extend([word_to_idx['<PAD>']] * (max_length - len(sequence)))
    else:
        sequence = sequence[:max_length]
    
    return sequence

def get_data_loaders(train_df, test_df, model_type='bert', batch_size=16, max_length=512):
    """Create data loaders for different model types"""
    
    if model_type == 'bert':
        tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
        
        train_dataset = TextDataset(
            train_df['cleaned_text'].values,
            train_df['encoded_label'].values,
            tokenizer=tokenizer,
            max_length=max_length
        )
        
        test_dataset = TextDataset(
            test_df['cleaned_text'].values,
            test_df['encoded_label'].values,
            tokenizer=tokenizer,
            max_length=max_length
        )
        
    elif model_type == 'lstm':
        # Create vocabulary
        word_to_idx, idx_to_word = create_vocab_for_lstm(train_df['cleaned_text'].values)
        
        # Convert texts to sequences
        train_sequences = [text_to_sequence(text, word_to_idx, max_length) 
                          for text in train_df['cleaned_text'].values]
        test_sequences = [text_to_sequence(text, word_to_idx, max_length) 
                         for text in test_df['cleaned_text'].values]
        
        train_dataset = TextDataset(
            train_sequences,
            train_df['encoded_label'].values,
            tokenizer=None
        )
        
        test_dataset = TextDataset(
            test_sequences,
            test_df['encoded_label'].values,
            tokenizer=None
        )
        
        # Return vocab info as well
        return (DataLoader(train_dataset, batch_size=batch_size, shuffle=True),
                DataLoader(test_dataset, batch_size=batch_size, shuffle=False),
                word_to_idx, idx_to_word)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader
