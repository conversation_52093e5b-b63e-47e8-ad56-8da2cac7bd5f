import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import time
import os

class LSTMClassifier(nn.Module):
    def __init__(self, vocab_size, embedding_dim=128, hidden_dim=256, num_layers=2, 
                 num_classes=2, dropout=0.3, bidirectional=True):
        super(LSTMClassifier, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        self.lstm = nn.LSTM(
            embedding_dim, 
            hidden_dim, 
            num_layers, 
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Calculate the size of the final hidden state
        lstm_output_size = hidden_dim * 2 if bidirectional else hidden_dim
        
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(lstm_output_size, num_classes)
        
    def forward(self, x):
        # x shape: (batch_size, seq_length)
        embedded = self.embedding(x)  # (batch_size, seq_length, embedding_dim)
        
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(embedded)
        
        # Use the last hidden state (for bidirectional, concatenate both directions)
        if self.lstm.bidirectional:
            # hidden shape: (num_layers * 2, batch_size, hidden_dim)
            # Take the last layer's hidden states from both directions
            hidden = torch.cat((hidden[-2], hidden[-1]), dim=1)
        else:
            # hidden shape: (num_layers, batch_size, hidden_dim)
            hidden = hidden[-1]  # Take the last layer
        
        # Apply dropout and classification layer
        output = self.dropout(hidden)
        output = self.fc(output)
        
        return output

class GRUClassifier(nn.Module):
    def __init__(self, vocab_size, embedding_dim=128, hidden_dim=256, num_layers=2, 
                 num_classes=2, dropout=0.3, bidirectional=True):
        super(GRUClassifier, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        self.gru = nn.GRU(
            embedding_dim, 
            hidden_dim, 
            num_layers, 
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Calculate the size of the final hidden state
        gru_output_size = hidden_dim * 2 if bidirectional else hidden_dim
        
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(gru_output_size, num_classes)
        
    def forward(self, x):
        # x shape: (batch_size, seq_length)
        embedded = self.embedding(x)  # (batch_size, seq_length, embedding_dim)
        
        # GRU forward pass
        gru_out, hidden = self.gru(embedded)
        
        # Use the last hidden state (for bidirectional, concatenate both directions)
        if self.gru.bidirectional:
            # hidden shape: (num_layers * 2, batch_size, hidden_dim)
            # Take the last layer's hidden states from both directions
            hidden = torch.cat((hidden[-2], hidden[-1]), dim=1)
        else:
            # hidden shape: (num_layers, batch_size, hidden_dim)
            hidden = hidden[-1]  # Take the last layer
        
        # Apply dropout and classification layer
        output = self.dropout(hidden)
        output = self.fc(output)
        
        return output

class LSTMGRUModel:
    def __init__(self, vocab_size, num_classes=2, model_type='lstm', 
                 embedding_dim=128, hidden_dim=256, num_layers=2, 
                 learning_rate=0.001, dropout=0.3):
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.vocab_size = vocab_size
        self.num_classes = num_classes
        self.model_type = model_type.lower()
        
        # Initialize model
        if self.model_type == 'lstm':
            self.model = LSTMClassifier(
                vocab_size, embedding_dim, hidden_dim, num_layers, 
                num_classes, dropout, bidirectional=True
            )
        elif self.model_type == 'gru':
            self.model = GRUClassifier(
                vocab_size, embedding_dim, hidden_dim, num_layers, 
                num_classes, dropout, bidirectional=True
            )
        else:
            raise ValueError("model_type must be 'lstm' or 'gru'")
        
        self.model.to(self.device)
        self.learning_rate = learning_rate
        self.is_trained = False
        self.training_history = []
        
    def train_model(self, train_loader, val_loader, epochs=25):
        """Train the LSTM/GRU model"""
        print(f"Training {self.model_type.upper()} model for {epochs} epochs on {self.device}...")
        
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # Training phase
            self.model.train()
            total_train_loss = 0
            train_predictions = []
            train_labels = []
            
            train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} - Training")
            for batch in train_pbar:
                optimizer.zero_grad()
                
                # Handle different data formats
                sequences = batch['text'].to(self.device)
                
                labels = batch['label'].to(self.device)
                
                outputs = self.model(sequences)
                loss = criterion(outputs, labels)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                optimizer.step()
                
                total_train_loss += loss.item()
                
                # Get predictions
                _, predicted = torch.max(outputs.data, 1)
                train_predictions.extend(predicted.cpu().numpy())
                train_labels.extend(labels.cpu().numpy())
                
                train_pbar.set_postfix({'loss': f'{loss.item():.4f}'})
            
            # Validation phase
            self.model.eval()
            total_val_loss = 0
            val_predictions = []
            val_labels = []
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} - Validation")
                for batch in val_pbar:
                    # Handle different data formats
                    sequences = batch['text'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = self.model(sequences)
                    loss = criterion(outputs, labels)
                    
                    total_val_loss += loss.item()
                    
                    _, predicted = torch.max(outputs.data, 1)
                    val_predictions.extend(predicted.cpu().numpy())
                    val_labels.extend(labels.cpu().numpy())
                    
                    val_pbar.set_postfix({'loss': f'{loss.item():.4f}'})
            
            # Update learning rate
            scheduler.step()
            
            # Calculate metrics
            avg_train_loss = total_train_loss / len(train_loader)
            avg_val_loss = total_val_loss / len(val_loader)
            train_acc = accuracy_score(train_labels, train_predictions)
            val_acc = accuracy_score(val_labels, val_predictions)
            
            epoch_time = time.time() - start_time
            
            # Store training history
            self.training_history.append({
                'epoch': epoch + 1,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
                'train_accuracy': train_acc,
                'val_accuracy': val_acc,
                'learning_rate': optimizer.param_groups[0]['lr'],
                'time': epoch_time
            })
            
            print(f"Epoch {epoch+1}/{epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}, Time: {epoch_time:.2f}s")
            print("-" * 50)
        
        self.is_trained = True
        print(f"{self.model_type.upper()} training completed!")
        return self.training_history
    
    def predict(self, sequences):
        """Make predictions on sequence data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        self.model.eval()
        predictions = []
        probabilities = []
        
        with torch.no_grad():
            # Convert to tensor if needed
            if not isinstance(sequences, torch.Tensor):
                sequences = torch.tensor(sequences)
            
            sequences = sequences.to(self.device)
            
            outputs = self.model(sequences)
            probs = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs.data, 1)
            
            predictions = predicted.cpu().numpy()
            probabilities = probs.cpu().numpy()
        
        return predictions, probabilities
    
    def evaluate(self, test_loader):
        """Evaluate model on test data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        self.model.eval()
        predictions = []
        labels = []
        probabilities = []
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="Evaluating"):
                # Handle different data formats
                sequences = batch['text'].to(self.device)
                batch_labels = batch['label'].to(self.device)
                
                outputs = self.model(sequences)
                probs = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs.data, 1)
                
                predictions.extend(predicted.cpu().numpy())
                labels.extend(batch_labels.cpu().numpy())
                probabilities.extend(probs.cpu().numpy())
        
        accuracy = accuracy_score(labels, predictions)
        report = classification_report(labels, predictions, output_dict=True)
        
        return {
            'accuracy': accuracy,
            'predictions': np.array(predictions),
            'probabilities': np.array(probabilities),
            'labels': np.array(labels),
            'classification_report': report
        }
    
    def save_model(self, filepath):
        """Save the trained model"""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'vocab_size': self.vocab_size,
            'num_classes': self.num_classes,
            'model_type': self.model_type,
            'training_history': self.training_history,
            'is_trained': self.is_trained
        }, filepath)
        
        print(f"{self.model_type.upper()} model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load a trained model"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Model file not found: {filepath}")
        
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.vocab_size = checkpoint['vocab_size']
        self.num_classes = checkpoint['num_classes']
        self.model_type = checkpoint['model_type']
        self.training_history = checkpoint['training_history']
        self.is_trained = checkpoint['is_trained']
        
        # Reinitialize model with correct parameters
        if self.model_type == 'lstm':
            self.model = LSTMClassifier(self.vocab_size, num_classes=self.num_classes)
        else:
            self.model = GRUClassifier(self.vocab_size, num_classes=self.num_classes)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        
        print(f"{self.model_type.upper()} model loaded from {filepath}")
